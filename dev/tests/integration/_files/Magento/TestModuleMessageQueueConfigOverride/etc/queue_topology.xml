<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework-message-queue:etc/topology.xsd">
    <exchange name="magento-topic-based-exchange1" connection="amqp">
        <arguments>
            <argument name="alternate-exchange" xsi:type="string">magento-log-exchange</argument>
        </arguments>
    </exchange>

    <exchange name="magento-topic-based-exchange2" type="topic" connection="amqp">
        <arguments>
            <argument name="alternate-exchange" xsi:type="string">magento-log-exchange</argument>
        </arguments>
        <binding id="topicBasedRouting2">
            <arguments>
                <argument name="argument2" xsi:type="boolean">true</argument>
                <argument name="argument3" xsi:type="number">150</argument>
            </arguments>
        </binding>
    </exchange>
</config>
