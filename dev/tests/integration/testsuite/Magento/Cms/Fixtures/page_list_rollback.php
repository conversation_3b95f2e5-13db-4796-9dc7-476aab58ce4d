<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

use Magento\Cms\Api\PageRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\TestFramework\Helper\Bootstrap;

$objectManager = Bootstrap::getObjectManager();

/** @var PageRepositoryInterface $pageRepository */
$pageRepository = $objectManager->get(PageRepositoryInterface::class);

/** @var SearchCriteriaBuilder $searchCriteriaBuilder */
$searchCriteriaBuilder = $objectManager->get(SearchCriteriaBuilder::class);
$searchCriteria = $searchCriteriaBuilder
    ->addFilter(
        'title',
        [
            'simplePage',
            'simplePage01',
            '01simplePage',
            'Page with 1column layout',
            'Page with unavailable layout',
        ],
        'in'
    )
    ->create();
$result = $pageRepository->getList($searchCriteria);

foreach ($result->getItems() as $item) {
    $pageRepository->delete($item);
}
