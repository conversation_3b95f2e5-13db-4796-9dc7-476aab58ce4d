<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<ruleset name="Magento Specific Design Rules"
         xmlns="http://pmd.sf.net/ruleset/1.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://pmd.sf.net/ruleset/1.0.0 http://pmd.sf.net/ruleset_xml_schema.xsd"
         xsi:noNamespaceSchemaLocation="http://pmd.sf.net/ruleset_xml_schema.xsd">
    <rule name="AllPurposeAction"
          class="Magento\CodeMessDetector\Rule\Design\AllPurposeAction"
          message= "The class {0} does not restrict processed HTTP methods by implementing a Http*Method name*ActionInterface">
        <description>
            <![CDATA[
Controllers (classes implementing ActionInterface) have to implement marker Http<Method>ActionInterface
to restrict incoming requests by methods.
            ]]>
        </description>
        <priority>2</priority>
        <properties />
        <example>
            <![CDATA[
class PostOrder implements ActionInterface
{
    public function execute()
    {
        //I process GET, POST, PATCH etc. while only intended for POST
        ...
        return $response;
    }
}
            ]]>
        </example>
    </rule>
    <rule name="CookieAndSessionMisuse"
          class="Magento\CodeMessDetector\Rule\Design\CookieAndSessionMisuse"
          message= "The class {0} uses sessions or cookies while not being a part of HTML Presentation layer">
        <description>
            <![CDATA[
Sessions and cookies must only be used in classes directly responsible for HTML presentation because Web APIs do not
rely on cookies and sessions. If you need to get current user use Magento\Authorization\Model\UserContextInterface
            ]]>
        </description>
        <priority>2</priority>
        <properties />
        <example>
            <![CDATA[
class OrderProcessor
{
    public function __construct(SessionManagerInterface $session) {
        $this->session = $session;
    }

    public function place(OrderInterface $order)
    {
        //Will not be present if processing a WebAPI request
        $currentOrder = $this->session->get('current_order');
        ...
    }
}
            ]]>
        </example>
    </rule>
</ruleset>
