---
tenant_params:
  fhr:
    shared:
      php_version: 8.1
      php_version_hold: 8.2
      php_version_hold_bool: false
      php_realpath_cache_size: 10M
      php_realpath_cache_ttl: 7200
      php_opcache_save_comments: 1
      php_upload_max_filesize: 10M
      php_post_max_size: 10M
      php_memory_limit: 4G
      nginx_version: "1.24.0-1~{{ ansible_distribution_release }}" # find available nginx versions with `apt-cache madison nginx`
      magento_dir: /var/www/magento
      magento_search_engine: elasticsearch7
      magento_db_name: magento
      mariadb_magento_db_user: magento-fhr-svc_usr
      magento_base_url: "https://{{ domain }}.fhr.se"
      nginx_domain_list:
        - fhr_se
        - fhrparts_com
      nginx_client_max_body_size: 10M
      backend_frontname: fhrcp
    test01:
      magento_elasticsearch_host: fhr-es-t1.test01.inkclub.local
      magento_redis_host: fhr-redis01t1.test01.inkclub.local
      magento_db_host: fhr-webdb01t1.test01.inkclub.local
      magento_varnish_host01_ip: *************
      magento_varnish_host02_ip: *************
      mail_relay: mx.inkclub.com
      git_branch: deploy/test01
    stage01:
      magento_elasticsearch_host: fhr-es-st.stage01.inkclub.local
      magento_redis_host: fhr-redis01st.stage01.inkclub.local
      magento_db_host: fhr-webdb01st.stage01.inkclub.local
      magento_varnish_host01_ip: *************
      magento_varnish_host02_ip: *************
      mail_relay: mx.inkclub.com
      git_branch: deploy/stage01
    prod:
      magento_elasticsearch_host: fhr-es-pr.prod.inkclub.local
      magento_redis_host: fhr-redis01pr.prod.inkclub.local
      magento_db_host: fhr-webdb01pr.prod.inkclub.local
      magento_varnish_host01_ip: *************
      magento_varnish_host02_ip: *************
      mail_relay: mx.inkclub.com
      git_branch: deploy/prod
  makewebo:
    shared:
      composer_version: 2.2.24
      php_version: 8.2
      php_version_hold: 8*
      php_version_hold_bool: false
      php_realpath_cache_size: 10M
      php_realpath_cache_ttl: 7200
      php_opcache_save_comments: 1
      php_upload_max_filesize: 10M
      php_post_max_size: 10M
      php_memory_limit: 4G
      nginx_version: 1.24
      nginx_version_package: "1.24.0-1~{{ansible_distribution_release}}" # find available nginx versions with `apt-cache madison nginx`
      magento_dir: /var/www/html/makewebo-ecommerce
      magento_search_engine: opensearch
      magento_db_name: magento
      mariadb_magento_db_user: magento-makewebo-svc_usr
      magento_base_url: "https://{{ domain }}.snushof.com"
      nginx_domain_list:
        - mysnus_com
        - nicobags_com
        - snus_at
        - snus_de
        - snuscentral_com
        - snusexpress_ch
        - snusexpress_com
        - snusexpress_se
        - snushof_ch
      nginx_client_max_body_size: 10M
      backend_frontname: admin
    test01:
      magento_elasticsearch_host: mw-es-t1.test01.inkclub.local
      magento_redis_host: mw-redis01t1.test01.inkclub.local
      magento_db_host: mw-webdb01t1.test01.inkclub.local
      magento_varnish_host01_ip: *************
      magento_varnish_host02_ip: *************
      mail_relay: mx.inkclub.com
      git_branch: develop
    stage01:
      magento_elasticsearch_host: mw-es-st.stage01.inkclub.local
      magento_redis_host: mw-redis01st.stage01.inkclub.local
      magento_db_host: mw-webdb01st.stage01.inkclub.local
      mail_relay: mx.inkclub.com
      git_branch: staging
      magento_varnish_host01_ip: *************
      magento_varnish_host02_ip: *************
    prod:
      magento_elasticsearch_host: mw-es-pr.prod.inkclub.local
      magento_redis_host: mw-redis01pr.prod.inkclub.local
      magento_db_host: mw-webdb01pr.prod.inkclub.local
      magento_varnish_host01_ip: *************
      magento_varnish_host02_ip: *************
      mail_relay: mx.inkclub.com
      git_branch: master
  ppn:
    shared:
      composer_version: 2.2.24
      php_version: 8.2
      php_version_hold: 8.2
      php_version_hold_bool: false
      php_realpath_cache_size: 10M
      php_realpath_cache_ttl: 7200
      php_opcache_save_comments: 1
      php_upload_max_filesize: 10M
      php_post_max_size: 10M
      php_memory_limit: 4G
      nginx_version: "1.24.0-1~{{ ansible_distribution_release }}" # find available nginx versions with `apt-cache madison nginx`
      nginx_version_package: "1.24.0-1~{{ansible_distribution_release}}" # find available nginx versions with `apt-cache madison nginx`
      magento_dir: /var/www/magento
      magento_search_engine: opensearch
      magento_db_name: magento
      mariadb_magento_db_user: magento
      magento_base_url: "https://{{ domain }}.3dprima.co.uk"
      nginx_domain_list:
        - 3dprima_co_uk
      nginx_client_max_body_size: 10M
      backend_frontname: admin
    test01:
      magento_elasticsearch_host: ppn-es-t1.test01.inkclub.local
      magento_redis_host: ppn-redis01t1.test01.inkclub.local
      magento_db_host: ppn-webdb01t1.test01.inkclub.local
      magento_varnish_host01_ip: *************
      magento_varnish_host02_ip: *************
      mail_relay: mx.inkclub.com
      git_branch: develop
    stage01:
      magento_elasticsearch_host: ppn-es-st.stage01.inkclub.local
      magento_redis_host: ppn-redis01st.stage01.inkclub.local
      magento_db_host: ppn-webdb01st.stage01.inkclub.local
      magento_varnish_host01_ip: *************
      magento_varnish_host02_ip: *************
      mail_relay: mx.inkclub.com
      git_branch: staging
    prod:
      magento_elasticsearch_host: ppn-es-pr.prod.inkclub.local
      magento_redis_host: ppn-redis01pr.prod.inkclub.local
      magento_db_host: ppn-webdb01pr.prod.inkclub.local
      magento_varnish_host01_ip: *************
      magento_varnish_host02_ip: *************
      mail_relay: mx.inkclub.com
  shared:
    nginx_dir: /etc/nginx
    nginx_webroot: /var/www 
    magento_admin_firstname: Magento
    magento_admin_lastname: Admin
    magento_admin_email: "magento-admin-{{tenant}}-{{domain}}@ipiccolo.com"
    magento_admin_user: magento-admin
    magento_timezone: Europe/Stockholm 
    magento_language: en_US
    magento_currency: SEK
    magento_use_rewrites: 1
    magento_elasticsearch_port: 9200
    magento_redis_port: 6379
    magento_app_service_usernames:
      - root
    local_magento_webapp_group: magento
    local_magento_webapp_gid: 2303
    local_magento_webapp_console_user: magento-cli
    local_magento_webapp_console_uid: 2302
    local_magento_webapp_console_gid: 2302

    local_magento_webapp_nginx_user: www-data
    local_magento_webapp_nginx_uid: 2301
    local_magento_webapp_nginx_gid: 2301
    local_magento_webapp_nginx_home: /var/www
    local_magento_webapp_nginx_group: www-data
