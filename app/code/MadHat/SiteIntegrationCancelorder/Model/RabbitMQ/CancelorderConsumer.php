<?php

namespace MadHat\SiteIntegrationCancelorder\Model\RabbitMQ;

use MadHat\DbLogger\Logger\DbLoggerSaver;
use MadHat\DbLogger\Model\Config\Source\LogIdentifierProvider;
use MadHat\SiteIntegrationCancelorder\Api\Data\CancelorderDataInterface;
use MadHat\SiteIntegrationCancelorder\Model\CancelorderProcessor;
use MadHat\SiteIntegrationCore\Helper\Data as SiteCoreHelper;
use Magento\Store\Model\App\Emulation;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class CancelorderConsumer
{
    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var CancelorderProcessor
     */
    private CancelorderProcessor $cancelorderProcessor;

    /**
     * @var DbLoggerSaver
     */
    private DbLoggerSaver $dbLoggerSaver;

    /**
     * @var Emulation
     */
    private Emulation $emulation;

    /**
     * @var StoreManagerInterface
     */
    private StoreManagerInterface $storeManager;

    /**
     * @var SiteCoreHelper
     */
    private SiteCoreHelper $siteCoreHelper;

    /**
     * CancelorderConsumer constructor.
     *
     * @param CancelorderProcessor $cancelorderProcessor
     * @param LoggerInterface $logger
     * @param DbLoggerSaver $dbLoggerSaver
     * @param Emulation $emulation
     * @param StoreManagerInterface $storeManager
     * @param SiteCoreHelper $siteCoreHelper
     */
    public function __construct(
        CancelorderProcessor $cancelorderProcessor,
        LoggerInterface $logger,
        DbLoggerSaver $dbLoggerSaver,
        Emulation $emulation,
        StoreManagerInterface $storeManager,
        SiteCoreHelper $siteCoreHelper
    ) {
        $this->cancelorderProcessor = $cancelorderProcessor;
        $this->logger = $logger;
        $this->dbLoggerSaver = $dbLoggerSaver;
        $this->emulation = $emulation;
        $this->storeManager = $storeManager;
        $this->siteCoreHelper = $siteCoreHelper;
    }

    /**
     * Consumer process start
     * @param CancelorderDataInterface[] $message
     * @return void
     */
    public function processMessage(array $message): void
    {
        // Check if RabbitMQ mode is enabled
        if (!$this->siteCoreHelper->isRabbitMqMode()) {
            $this->logger->info(__(
                '%1 => %2 Skipping message processing - Integration mode is not set to RabbitMQ',
                __CLASS__,
                __FUNCTION__
            ));
            return;
        }

        $this->logger->info(__(
            '%1 => %2 Received Message: %3',
            __CLASS__,
            __FUNCTION__,
            print_r($message, true)
        ));

        $totalMessages = count($message);
        $successfulMessages = 0;
        $failedMessages = 0;
        $errors = [];

        try {
            foreach ($message as $item) {
                try {
                    $this->cancelorderProcessor->processItem($item);
                    $this->addDbLoggerCancelOrderRecord(1, 1, 0, []);
                } catch (\Exception $e) {
                    $this->addDbLoggerCancelOrderRecord(1, 0, 1, [
                        [
                            'Item' => $item,
                            'ErrorMessage' => $e->getMessage()
                        ]
                    ]);
                    $this->logger->error('Error processing item: ' . $e->getMessage());
                }
            }
        } catch (\InvalidArgumentException $exception) {
            $this->logger->error(__(
                '%1 => %2 ERROR: %3',
                __CLASS__,
                __FUNCTION__,
                $exception->getMessage()
            ));
        }
    }

    /**
     * Function to log the processing results
     *
     * @param int $totalMessages
     * @param int $successfulMessages
     * @param int $failedMessages
     * @param array $errors
     * @return void
     */
    private function addDbLoggerCancelOrderRecord(int $totalMessages, int $successfulMessages, int $failedMessages, array $errors): void
    {
        $message = "Total Messages: " . $totalMessages;
        $message .= " | Successful Messages: " . $successfulMessages;
        $message .= " | Failed Messages: " . $failedMessages;

        if (!empty($errors)) {
            $message .= " | Errors: " . json_encode($errors);
        }

        $defaultStoreId = $this->storeManager->getDefaultStoreView()->getId();

        if (!$defaultStoreId) {
            $defaultStoreId = 1;
        }

        $this->emulation->startEnvironmentEmulation($defaultStoreId);
        $this->dbLoggerSaver->addRecord(
            'Cancel Order Import Report',
            $message,
            'NOTICE',
            LogIdentifierProvider::CANCEL_ORDER
        );
        $this->emulation->stopEnvironmentEmulation();
    }
}
