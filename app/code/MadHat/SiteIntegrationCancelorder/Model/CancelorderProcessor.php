<?php

namespace MadHat\SiteIntegrationCancelorder\Model;

use MadHat\SiteIntegrationCancelorder\Api\Data\CancelorderDataInterface;
use MadHat\SiteIntegrationCore\Model\Api\SiteOrderApi\DatabaseOperations;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Api\Data\OrderInterface;
use Psr\Log\LoggerInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\LocalizedException;

class CancelorderProcessor
{
    /** @var LoggerInterface */
    private LoggerInterface $logger;

    /** @var OrderRepositoryInterface */
    private OrderRepositoryInterface $orderRepository;

    /** @var DatabaseOperations */
    private DatabaseOperations $databaseOperations;

    /** @var \Magento\Framework\Api\SearchCriteriaBuilder */
    private $searchCriteriaBuilder;

    /**
     * @param OrderRepositoryInterface $orderRepository
     * @param DatabaseOperations $databaseOperations
     * @param LoggerInterface $logger
     * @param \Magento\Framework\Api\SearchCriteriaBuilder $searchCriteriaBuilder
     */
    public function __construct(
        OrderRepositoryInterface $orderRepository,
        DatabaseOperations $databaseOperations,
        LoggerInterface $logger,
        \Magento\Framework\Api\SearchCriteriaBuilder $searchCriteriaBuilder
    ) {
        $this->orderRepository = $orderRepository;
        $this->databaseOperations = $databaseOperations;
        $this->logger = $logger;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
    }

    /**
     * @param CancelorderDataInterface $item
     * @return bool
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function processItem(CancelorderDataInterface $item): bool
    {
        $webOrderNo = $item->getWebOrderNo();
        if (!$webOrderNo) {
            $this->logger->warning('WebOrderNo is missing in the message.');
            return false;
        }

        try {
            $order = $this->loadOrderByIncrementId($webOrderNo);
            if ($order && $this->databaseOperations->directCancelOrder($order)) {
                $this->logger->info(sprintf('Order with WebOrderNo %s has been canceled.', $webOrderNo));
                return true;
            }
            throw new \Exception(sprintf('Order with WebOrderNo %s could not be canceled.', $webOrderNo));
        } catch (NoSuchEntityException $e) {
            $this->logger->error(sprintf('Order with WebOrderNo %s not found.', $webOrderNo));
            throw $e;
        } catch (LocalizedException $e) {
            $this->logger->error(sprintf('Error canceling order with WebOrderNo %s: %s', $webOrderNo, $e->getMessage()));
            throw $e;
        } catch (\Exception $e) {
            $this->logger->critical(sprintf('An unexpected error occurred while canceling order with WebOrderNo %s: %s', $webOrderNo, $e->getMessage()));
            throw $e;
        }
    }

    /**
     * @param string $incrementId
     * @return OrderInterface|null
     */
    private function loadOrderByIncrementId(string $incrementId): ?OrderInterface
    {
        $this->searchCriteriaBuilder->addFilter('increment_id', $incrementId);
        $orderList = $this->orderRepository->getList($this->searchCriteriaBuilder->create())->getItems();

        if (empty($orderList)) {
            return null;
        }

        return reset($orderList);
    }
}
