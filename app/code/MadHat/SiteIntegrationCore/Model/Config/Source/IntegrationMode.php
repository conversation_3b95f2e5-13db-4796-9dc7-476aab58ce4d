<?php
declare(strict_types=1);

namespace MadHat\SiteIntegrationCore\Model\Config\Source;

use Magento\Framework\Data\OptionSourceInterface;

class IntegrationMode implements OptionSourceInterface
{
    public const MODE_CRON = 'cron';
    public const MODE_RABBITMQ = 'rabbitmq';

    /**
     * Return array of options as value-label pairs
     *
     * @return array
     */
    public function toOptionArray(): array
    {
        return [
            ['value' => self::MODE_CRON, 'label' => __('Cron (Scheduled Tasks) - Polling by cron scheduler settings')],
            ['value' => self::MODE_RABBITMQ, 'label' => __('RabbitMQ (Message Queue) - Real-time processing')],
        ];
    }

    /**
     * Get options in "key-value" format
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::MODE_CRON => __('Cron (Scheduled Tasks) - Polling by cron scheduler settings'),
            self::MODE_RABBITMQ => __('RabbitMQ (Message Queue) - Real-time processing'),
        ];
    }
}
