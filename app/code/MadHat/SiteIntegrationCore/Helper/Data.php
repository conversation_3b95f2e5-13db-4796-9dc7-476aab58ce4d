<?php
declare(strict_types=1);

namespace MadHat\SiteIntegrationCore\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\Encryption\EncryptorInterface;
use Magento\Store\Model\ScopeInterface;
use MadHat\SiteIntegrationCore\Model\Config\Source\IntegrationMode;

class Data extends AbstractHelper
{
    /**
     * Config path for SiteIntegration Core 'API URL' config
     */
    protected const SITE_CORE_URL_KEY_PATH = "site_core/general/api_url";

    /**
     * Config path for SiteIntegration Core 'Company ID' config
     */
    protected const SITE_CORE_COMPANY_ID_PATH = "site_core/general/company_id";

    /**
     * Config path for SiteIntegration Core 'Secret Key' config
     */
    protected const SITE_CORE_SECRET_KEY_PATH = "site_core/general/secret_key";

    /**
     * Config path for SiteIntegration Core 'Integration Mode' config
     */
    protected const SITE_CORE_INTEGRATION_MODE_PATH = "site_core/general/integration_mode";

    /**
     * @var EncryptorInterface
     */
    protected EncryptorInterface $encryptor;

    /**
     * @param Context $context
     * @param EncryptorInterface $encryptor
     */
    public function __construct(
        Context $context,
        EncryptorInterface $encryptor,
    ) {
        parent::__construct($context);
        $this->encryptor = $encryptor;
    }

    /**
     * Returns value for 'API URL' config
     *
     * @param string $websiteId
     * @return string
     */
    public function getApiUrl(string $websiteId = ''): string
    {
        return $this->scopeConfig->getValue(
            self::SITE_CORE_URL_KEY_PATH,
            ScopeInterface::SCOPE_WEBSITE,
            $websiteId
        );
    }

    /**
     * Returns value for 'Company ID' config
     *
     * @param string $websiteId
     * @return string
     */
    public function getCompanyId(string $websiteId = ''): string
    {
        return $this->scopeConfig->getValue(
            self::SITE_CORE_COMPANY_ID_PATH,
            ScopeInterface::SCOPE_WEBSITE,
            $websiteId
        );
    }

    /**
     * Returns value for 'Secret Key' config
     *
     * @param string $websiteId
     * @return string
     */
    public function getSecretKey(string $websiteId = ''): string
    {
        $secretKey = $this->scopeConfig->getValue(
            self::SITE_CORE_SECRET_KEY_PATH,
            ScopeInterface::SCOPE_WEBSITE,
            $websiteId
        );

        return $this->encryptor->decrypt($secretKey);
    }

    /**
     * Returns value for 'Integration Mode' config
     *
     * @param int $websiteId
     * @return string
     */
    public function getIntegrationMode(int $websiteId = 0): string
    {
        return $this->scopeConfig->getValue(
            self::SITE_CORE_INTEGRATION_MODE_PATH,
            ScopeInterface::SCOPE_WEBSITE,
            $websiteId
        ) ?: IntegrationMode::MODE_CRON;
    }

    /**
     * Check if integration mode is set to Cron
     *
     * @param int $websiteId
     * @return bool
     */
    public function isCronMode(int $websiteId = 0): bool
    {
        return $this->getIntegrationMode($websiteId) === IntegrationMode::MODE_CRON;
    }

    /**
     * Check if integration mode is set to RabbitMQ
     *
     * @param int $websiteId
     * @return bool
     */
    public function isRabbitMqMode(int $websiteId = 0): bool
    {
        return $this->getIntegrationMode($websiteId) === IntegrationMode::MODE_RABBITMQ;
    }
}
