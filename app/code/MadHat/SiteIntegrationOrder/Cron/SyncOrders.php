<?php
declare(strict_types=1);

namespace MadHat\SiteIntegrationOrder\Cron;

use MadHat\SiteIntegrationCore\Helper\Data as SiteCoreHelper;
use MadHat\SiteIntegrationOrder\Helper\Data;
use MadHat\SiteIntegrationOrder\Model\Api\SiteOrderApi;

class SyncOrders
{
    /**
     * @var Data
     */
    private Data $siteOrderHelper;

    /**
     * @var SiteOrderApi
     */
    private SiteOrderApi $siteOrderApi;

    /**
     * @var SiteCoreHelper
     */
    private SiteCoreHelper $siteCoreHelper;

    /**
     * @param Data $siteOrderHelper
     * @param SiteOrderApi $siteOrderApi
     * @param SiteCoreHelper $siteCoreHelper
     */
    public function __construct(
        Data         $siteOrderHelper,
        SiteOrderApi $siteOrderApi,
        SiteCoreHelper $siteCoreHelper
    ) {
        $this->siteOrderHelper = $siteOrderHelper;
        $this->siteOrderApi = $siteOrderApi;
        $this->siteCoreHelper = $siteCoreHelper;
    }

    /**
     * Export Magento orders to SITE
     *
     * @return void
     */
    public function execute(): void
    {
        if ($this->siteOrderHelper->getIsEnabled() && $this->siteCoreHelper->isCronMode()) {
            $this->siteOrderApi->syncSiteOrders();
        }
    }
}
