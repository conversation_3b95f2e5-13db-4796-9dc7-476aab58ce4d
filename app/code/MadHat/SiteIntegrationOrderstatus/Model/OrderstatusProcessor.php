<?php

namespace MadHat\SiteIntegrationOrderstatus\Model;

use MadHat\DbLogger\Logger\DbLoggerSaver;
use MadHat\DbLogger\Model\Config\Source\LogIdentifierProvider;
use MadHat\SiteIntegrationCore\Model\Api\SiteOrderApi\OrderSynchronizer;
use MadHat\SiteIntegrationOrderstatus\Api\Data\OrderstatusDataInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\OrderRepositoryInterface;

class OrderstatusProcessor
{
    /** @var OrderRepositoryInterface */
    private OrderRepositoryInterface $orderRepository;

    /** @var SearchCriteriaBuilder */
    private SearchCriteriaBuilder $searchCriteriaBuilder;

    /** @var OrderSynchronizer */
    private OrderSynchronizer $orderSynchronizer;

    /** @var DbLoggerSaver */
    private DbLoggerSaver $dbLoggerSaver;

    /**
     * @param OrderRepositoryInterface $orderRepository
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param OrderSynchronizer $orderSynchronizer
     * @param DbLoggerSaver $dbLoggerSaver
     */
    public function __construct(
        OrderRepositoryInterface $orderRepository,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        OrderSynchronizer $orderSynchronizer,
        DbLoggerSaver $dbLoggerSaver
    ) {
        $this->orderRepository = $orderRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->orderSynchronizer = $orderSynchronizer;
        $this->dbLoggerSaver = $dbLoggerSaver;
    }

    /**
     * Process RabbitMQ order status message by triggering SITE synchronization
     *
     * This method receives a RabbitMQ message about an order status change and triggers
     * the same synchronization logic used by console/cron commands. Instead of processing
     * the RabbitMQ data directly, it calls the SITE API to get the latest order status
     * and processes it using the same OrderSynchronizer service.
     *
     * @param OrderstatusDataInterface $item
     * @return bool
     */
    public function processItem(OrderstatusDataInterface $item): bool
    {
        $webOrderNo = $item->getWebOrderNo();
        if (!$webOrderNo) {
            $this->dbLoggerSaver->addRecord(
                'RabbitMQ Order Sync Error',
                'WebOrderNo is missing in the message.',
                'warning',
                LogIdentifierProvider::ORDER
            );
            return false;
        }

        try {
            // WebOrderNo in RabbitMQ is actually the entity_id, not increment_id
            $order = $this->loadOrderByEntityId((int)$webOrderNo);
            if (!$order) {
                $this->dbLoggerSaver->addRecord(
                    'RabbitMQ Order Sync Error',
                    sprintf('Order with WebOrderNo %s (entity_id) not found.', $webOrderNo),
                    'warning',
                    LogIdentifierProvider::ORDER
                );
                return false;
            }

            $this->dbLoggerSaver->addRecord(
                'RabbitMQ Order Sync Processing',
                sprintf(
                    'RabbitMQ message received for order %s (entity_id: %s). Triggering SITE synchronization. Status from message: %s (%s)',
                    $order->getIncrementId(),
                    $webOrderNo,
                    $item->getStatusText(),
                    $item->getStatus()
                ),
                'info',
                LogIdentifierProvider::ORDER
            );

            // Use the same synchronization logic as console/cron by calling OrderSynchronizer
            // This will query SITE API for the latest order status and process it properly
            $syncResult = $this->orderSynchronizer->syncSiteOrders([$order->getIncrementId()]);

            if ($syncResult['success'] && $syncResult['success_orders'] > 0) {
                $this->dbLoggerSaver->addRecord(
                    'RabbitMQ Order Sync Success',
                    sprintf(
                        'Successfully synchronized order %s via RabbitMQ trigger. Result: %s',
                        $order->getIncrementId(),
                        $syncResult['message']
                    ),
                    'info',
                    LogIdentifierProvider::ORDER
                );
                return true;
            } else {
                $errorMessage = 'Unknown sync error';
                if (!empty($syncResult['error_messages'])) {
                    $errorMessage = json_encode($syncResult['error_messages']);
                } elseif (isset($syncResult['message'])) {
                    $errorMessage = $syncResult['message'];
                }

                $this->dbLoggerSaver->addRecord(
                    'RabbitMQ Order Sync Error',
                    sprintf(
                        'Failed to synchronize order %s via RabbitMQ trigger. Error: %s',
                        $order->getIncrementId(),
                        $errorMessage
                    ),
                    'error',
                    LogIdentifierProvider::ORDER
                );
                return false;
            }

        } catch (NoSuchEntityException $e) {
            $this->dbLoggerSaver->addRecord(
                'RabbitMQ Order Sync Error',
                sprintf('Order with WebOrderNo %s not found: %s', $webOrderNo, $e->getMessage()),
                'error',
                LogIdentifierProvider::ORDER
            );
            return false;
        } catch (LocalizedException $e) {
            $this->dbLoggerSaver->addRecord(
                'RabbitMQ Order Sync Error',
                sprintf('Error processing order with WebOrderNo %s: %s', $webOrderNo, $e->getMessage()),
                'error',
                LogIdentifierProvider::ORDER
            );
            return false;
        } catch (\Exception $e) {
            $this->dbLoggerSaver->addRecord(
                'RabbitMQ Order Sync Error',
                sprintf('Unexpected error processing order with WebOrderNo %s: %s', $webOrderNo, $e->getMessage()),
                'error',
                LogIdentifierProvider::ORDER
            );
            return false;
        }
    }

    /**
     * Load order by entity ID
     *
     * @param int $entityId
     * @return OrderInterface|null
     */
    private function loadOrderByEntityId(int $entityId): ?OrderInterface
    {
        try {
            return $this->orderRepository->get($entityId);
        } catch (NoSuchEntityException $e) {
            return null;
        }
    }
}
