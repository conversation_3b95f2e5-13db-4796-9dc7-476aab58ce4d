<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Communication/etc/communication.xsd">

<!--    <topic name="site.techoutlet.orderstatus" request="MadHat\SiteIntegrationOrderstatus\Api\Data\OrderstatusDataInterface[]">-->
<!--        <handler name="madhat.siteintegrationorderstatus.consumer" type="MadHat\SiteIntegrationOrderstatus\Model\RabbitMQ\OrderstatusConsumer" method="processMessage"/>-->
<!--    </topic>-->

    <!-- For Developer Only -->
    <topic name="site.techoutlet-dev.orderstatus.olegh" request="MadHat\SiteIntegrationOrderstatus\Api\Data\OrderstatusDataInterface[]">
        <handler name="madhat.siteintegrationorderstatus.consumer" type="MadHat\SiteIntegrationOrderstatus\Model\RabbitMQ\OrderstatusConsumer" method="processMessage"/>
    </topic>
</config>
