<?php

namespace MadHat\DataUpdater\Setup\Patch\Data;

use Magento\Framework\Exception\FileSystemException;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Catalog\Model\CategoryFactory;
use Magento\Catalog\Model\ResourceModel\Category as CategoryResource;
use Magento\Framework\Filesystem\Driver\File;
use Magento\Framework\DB\Adapter\AdapterInterface;
use Magento\Framework\App\ResourceConnection;
use Psr\Log\LoggerInterface;

/**
 * Class UpdateCategoryV2
 *
 * This data patch provides functionality to:
 * 1) Create or update Magento categories from a CSV file.
 * 2) Modify a Ninja Menus “top-menu” JSON structure to add, remove,
 *    or replace menu items (e.g., categories) programmatically.
 *
 * ### CSV Format
 *
 * The CSV file must have these columns (example):
 *   "Category ID","MadHat External Cat Id","Category Parent External ID","Parent Category ID","Title","Menu"
 *
 * - Category ID:
 *     The numeric Magento category ID. If empty, the script will search or
 *     create a category by "MadHat External Cat Id" instead.
 *
 * - MadHat External Cat Id:
 *     A custom attribute used to uniquely identify categories externally.
 *     If "Category ID" is empty, we use this to load or create the category.
 *
 * - Category Parent External ID:
 *     The custom external ID of the parent category. (Only used if "Parent Category ID" is empty)
 *
 * - Parent Category ID:
 *     The Magento numeric parent category ID. If this is present and not empty,
 *     it overrides the external ID approach.
 *
 * - Title:
 *     The name for the category or menu item.
 *
 * - Menu:
 *     A special instruction for how to manipulate the Ninja Menus JSON. Format:
 *     "<Parent Title>=<op>><Target Title>"
 *     Where <op> is one of:
 *       "=+>" => Insert new menu item AFTER <Target Title> under <Parent Title>
 *       "=->" => Delete menu item with title <Target Title> under <Parent Title>
 *       "=~>" => Replace menu item <Target Title> with new Title/category_id
 *
 * ### Usage
 *
 * - Place your CSV as "UpdateCategoryV2.csv" in the same directory as this class.
 * - Run "bin/magento setup:upgrade" or otherwise deploy the data patch.
 * - The script logs actions to the system's log and modifies the top-menu JSON in `mgz_ninjamenus_menu`.
 * - Before modifying the top-menu, the existing `profile` JSON field from `mgz_ninjamenus_menu` is **backed up**
 *   to a file named `mgz_ninjamenus_menu_profile_<timestamp>.json` (e.g., `mgz_ninjamenus_menu_profile_20250127132212.json`)
 *   under `var/` in your Magento root.
 */
class UpdateCategoryV2 implements DataPatchInterface
{
    /** @var CategoryFactory */
    protected $categoryFactory;

    /** @var CategoryResource */
    protected $categoryResource;

    /** @var File */
    protected $fileDriver;

    /** @var ResourceConnection */
    protected $resourceConnection;

    /** @var LoggerInterface */
    protected $logger;

    /**
     * Constructor
     *
     * @param CategoryFactory    $categoryFactory
     * @param CategoryResource   $categoryResource
     * @param File               $fileDriver
     * @param ResourceConnection $resourceConnection
     * @param LoggerInterface    $logger
     */
    public function __construct(
        CategoryFactory $categoryFactory,
        CategoryResource $categoryResource,
        File $fileDriver,
        ResourceConnection $resourceConnection,
        LoggerInterface $logger
    ) {
        $this->categoryFactory    = $categoryFactory;
        $this->categoryResource   = $categoryResource;
        $this->fileDriver         = $fileDriver;
        $this->resourceConnection = $resourceConnection;
        $this->logger             = $logger;
    }

    /**
     * Execute the data patch:
     * - Reads rows from the CSV file
     * - Creates/updates categories
     * - Modifies the "top-menu" JSON in `mgz_ninjamenus_menu` according to CSV "Menu" instructions
     *
     * @return void
     * @throws FileSystemException
     */
    public function apply()
    {
        $this->logger->info("UpdateCategoryV2: Starting category updates.");

        $csvFilePath = __DIR__ . '/UpdateCategoryV2.csv';
        if (!$this->fileDriver->isExists($csvFilePath)) {
            $this->logger->warning("UpdateCategoryV2: CSV file not found at $csvFilePath. Skipping updates.");
            return;
        }

        try {
            $rows = array_map('str_getcsv', explode(PHP_EOL, $this->fileDriver->fileGetContents($csvFilePath)));
            $headers = array_shift($rows);

            $connection = $this->resourceConnection->getConnection();
            $menuTable = $connection->getTableName('mgz_ninjamenus_menu');

            // Backup existing "profile" JSON from mgz_ninjamenus_menu
            $this->backupProfileField($connection, $menuTable);

            foreach ($rows as $index => $row) {
                // Skip empty or invalid rows
                if (empty($row) || count(array_filter($row)) === 0) {
                    $this->logger->warning("UpdateCategoryV2: Skipping empty row at index $index.");
                    continue;
                }
                if (count($headers) !== count($row)) {
                    $this->logger->warning(
                        "UpdateCategoryV2: Mismatched header and row data at index $index. Skipping row. "
                        . "Headers: " . json_encode($headers) . ", Row: " . json_encode($row)
                    );
                    continue;
                }

                // Combine the CSV data into an associative array
                $data = array_combine($headers, $row);

                // 1) Create or update the Category
                if (empty($data['Category ID'])) {
                    $this->logger->info("UpdateCategoryV2: Checking by MadHat External Cat Id.");
                    $this->updateOrCreateCategoryByExternalId($data);
                } else {
                    $this->logger->info("UpdateCategoryV2: Processing category ID " . $data['Category ID']);
                    $this->updateCategoryAttributes($data);
                }

                // 2) If there's a "Menu" instruction, modify the top-menu JSON
                if (!empty($data['Menu'])) {
                    $menuJson = $this->updateMenuJson($connection, $menuTable, $data['Menu'], $data);

                    if (!empty($menuJson)) {
                        $connection->update(
                            $menuTable,
                            ['profile' => json_encode($menuJson, JSON_UNESCAPED_SLASHES)],
                            ['identifier = ?' => 'top-menu']
                        );
                        $this->logger->info("UpdateCategoryV2: Persisted updated menu JSON to database.");
                    }
                }
            }
        } catch (\Exception $e) {
            $this->logger->error("UpdateCategoryV2: Error during update process: " . $e->getMessage());
        }

        $this->logger->info("UpdateCategoryV2: Finished category updates.");
    }

    /**
     * Creates a backup of the existing "profile" JSON in `mgz_ninjamenus_menu` for the "top-menu" identifier.
     *
     * @param AdapterInterface $connection
     * @param string           $menuTable
     * @return void
     */
    protected function backupProfileField($connection, $menuTable)
    {
        try {
            $profileData = $connection->fetchOne(
                $connection->select()
                    ->from($menuTable, ['profile'])
                    ->where('identifier = ?', 'top-menu')
            );

            if ($profileData) {
                $backupFileName = BP . '/var/mgz_ninjamenus_menu_profile_' . date('YmdHis') . '.json';
                $this->fileDriver->filePutContents($backupFileName, $profileData);
                $this->logger->info("UpdateCategoryV2: Profile field backed up to " . $backupFileName);
            } else {
                $this->logger->warning("UpdateCategoryV2: No profile data found to backup.");
            }
        } catch (\Exception $e) {
            $this->logger->error("UpdateCategoryV2: Failed to backup profile field: " . $e->getMessage());
        }
    }

    /**
     * If the CSV row doesn't have a Category ID, we look up or create a category by "MadHat External Cat Id".
     * If found, we update it. Otherwise, we create a new category and store its numeric ID back to the CSV data array.
     *
     * @param array $data
     * @return void
     */
    protected function updateOrCreateCategoryByExternalId(array &$data)
    {
        try {
            $category = $this->categoryFactory->create();
            $attribute = $category->getResource()->getAttribute('madhat_ex_cat_id');
            if (!$attribute) {
                throw new \Exception("The \"madhat_ex_cat_id\" attribute name is invalid.");
            }

            // Try to find existing category by external ID
            $existingCategory = $category->getCollection()
                ->addAttributeToFilter('madhat_ex_cat_id', $data['MadHat External Cat Id'])
                ->getFirstItem();

            if ($existingCategory && $existingCategory->getId()) {
                $this->logger->info("...Existing category found by MadHat External Cat Id. Updating category.");
                $this->updateCategoryAttributesByInstance($existingCategory, $data);
                // Store final ID in CSV data so we can later use it in the menu
                $data['Category ID'] = $existingCategory->getId();
            } else {
                // No existing category => create new
                $this->logger->info("...No category found by MadHat External Cat Id. Creating new category.");
                $newCategoryId = $this->createNewCategory($data);
                if ($newCategoryId) {
                    $data['Category ID'] = $newCategoryId;
                }
            }
        } catch (\Exception $e) {
            $this->logger->error("...Failed to update/create category by External Id: " . $e->getMessage());
        }
    }

    /**
     * Create a new category based on CSV data, then return its numeric ID.
     *
     * Steps:
     * - Determine parent by either "Parent Category ID" or "Category Parent External ID" (or default to root).
     * - Create the new category, set it active, and include it in the menu.
     * - Update its path if the parent is not root.
     *
     * @param array $data
     * @return int|null The new category ID or null on failure
     */
    protected function createNewCategory(array $data)
    {
        try {
            // Default parent is root (ID=2)
            $parentCategoryId   = 2;
            $parentCategoryPath = null;

            // 1) If "Parent Category ID" is present and not empty, we use that.
            if (!empty($data['Parent Category ID'])) {
                $parent = $this->categoryFactory->create()->load($data['Parent Category ID']);
                if ($parent && $parent->getId()) {
                    $parentCategoryId   = $parent->getId();
                    $parentCategoryPath = $parent->getPath();
                } else {
                    throw new \Exception(
                        "Parent cat with ID '{$data['Parent Category ID']}' not found."
                    );
                }
            }
            // 2) Otherwise, if "Category Parent External ID" is present, find by external ID
            elseif (!empty($data['Category Parent External ID'])) {
                $parent = $this->categoryFactory->create()->getCollection()
                    ->addAttributeToFilter('madhat_ex_cat_id', $data['Category Parent External ID'])
                    ->getFirstItem();
                if ($parent && $parent->getId()) {
                    $parentCategoryId   = $parent->getId();
                    $parentCategoryPath = $parent->getPath();
                } else {
                    throw new \Exception(
                        "Parent cat with External Id '{$data['Category Parent External ID']}' not found."
                    );
                }
            }

            $category = $this->categoryFactory->create();
            $category->setData('madhat_ex_cat_id', $data['MadHat External Cat Id']);
            $category->setData('parent_id', $parentCategoryId);
            $category->setData('name', $data['Title']);
            $category->setIsActive(true);
            $category->setIncludeInMenu(true);

            $this->categoryResource->save($category);

            // Update path if parent is non-root
            $newId = $category->getId();
            if ($newId && $parentCategoryPath) {
                $category->setPath($parentCategoryPath . '/' . $newId);
                $this->categoryResource->save($category);
            }

            $this->logger->info("...Successfully created new category '{$data['Title']}' with ID {$newId}.");
            return $newId;
        } catch (\Exception $e) {
            $this->logger->error("...Failed to create new category: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Update the category attributes if the "Category ID" is provided in CSV.
     * This is typically called if CSV row has a numeric ID already.
     *
     * We also handle changing parent by either "Parent Category ID" or
     * searching for "Category Parent External ID" if "Parent Category ID" is empty.
     *
     * @param array $data
     * @return void
     */
    protected function updateCategoryAttributes(array &$data)
    {
        $this->logger->info("...Updating attributes for category ID {$data['Category ID']}");

        try {
            /** @var \Magento\Catalog\Model\Category $category */
            $category = $this->categoryFactory->create()->load($data['Category ID']);
            if (!$category->getId()) {
                $this->logger->warning("...Category ID {$data['Category ID']} not found. Skipping.");
                return;
            }

            // Decide parent ID
            $parentCategoryId   = null;
            $parentCategoryPath = null;

            // If "Parent Category ID" is present and not empty, use that
            if (!empty($data['Parent Category ID'])) {
                $parent = $this->categoryFactory->create()->load($data['Parent Category ID']);
                if ($parent && $parent->getId()) {
                    $parentCategoryId   = $parent->getId();
                    $parentCategoryPath = $parent->getPath();
                } else {
                    $this->logger->warning(
                        "...Could not find parent category with ID '{$data['Parent Category ID']}'. Skipping parent update."
                    );
                }
            }
            // Else if "Category Parent External ID" is present, load the parent by that external ID
            elseif (!empty($data['Category Parent External ID'])) {
                $parent = $this->categoryFactory->create()->getCollection()
                    ->addAttributeToFilter('madhat_ex_cat_id', $data['Category Parent External ID'])
                    ->getFirstItem();
                if ($parent && $parent->getId()) {
                    $parentCategoryId   = $parent->getId();
                    $parentCategoryPath = $parent->getPath();
                } else {
                    $this->logger->warning(
                        "...Could not find parent category with External ID '{$data['Category Parent External ID']}'. " .
                        "Skipping parent update."
                    );
                }
            }

            // Update basic attributes
            $category->setData('madhat_ex_cat_id', $data['MadHat External Cat Id']);
            $category->setData('name', $data['Title']);

            // If we found a valid parent ID, update parent
            if ($parentCategoryId) {
                $category->setData('parent_id', $parentCategoryId);
            }

            $this->categoryResource->save($category);

            // Potentially fix path if we changed the parent
            if ($parentCategoryId && $parentCategoryPath) {
                $category->setPath($parentCategoryPath . '/' . $category->getId());
                $this->categoryResource->save($category);
            }

            // Ensure the final ID is stored back into the CSV data
            $data['Category ID'] = $category->getId();

            $this->logger->info("...Successfully updated category #{$data['Category ID']} ('{$data['Title']}').");
        } catch (\Exception $e) {
            $this->logger->error("...Failed to update attributes for category ID {$data['Category ID']}: {$e->getMessage()}");
        }
    }

    /**
     * Update category attributes by an existing Category instance (already loaded).
     * Similar logic to "updateCategoryAttributes" but re-uses the loaded instance.
     *
     * @param \Magento\Catalog\Model\Category $category
     * @param array                           $data
     * @return void
     */
    protected function updateCategoryAttributesByInstance($category, array &$data)
    {
        try {
            // Decide parent ID
            $parentCategoryId   = null;
            $parentCategoryPath = null;

            // Check for "Parent Category ID" first
            if (!empty($data['Parent Category ID'])) {
                $parent = $this->categoryFactory->create()->load($data['Parent Category ID']);
                if ($parent && $parent->getId()) {
                    $parentCategoryId   = $parent->getId();
                    $parentCategoryPath = $parent->getPath();
                } else {
                    $this->logger->warning(
                        "...Could not find parent category with ID '{$data['Parent Category ID']}'. Skipping parent update."
                    );
                }
            }
            // Otherwise fallback to "Category Parent External ID"
            elseif (!empty($data['Category Parent External ID'])) {
                $parent = $this->categoryFactory->create()->getCollection()
                    ->addAttributeToFilter('madhat_ex_cat_id', $data['Category Parent External ID'])
                    ->getFirstItem();
                if ($parent && $parent->getId()) {
                    $parentCategoryId   = $parent->getId();
                    $parentCategoryPath = $parent->getPath();
                } else {
                    $this->logger->warning(
                        "...Could not find parent category with External ID '{$data['Category Parent External ID']}'. " .
                        "Skipping parent update."
                    );
                }
            }

            // Update basic attributes
            $category->setData('madhat_ex_cat_id', $data['MadHat External Cat Id']);
            $category->setData('name', $data['Title']);

            // If we found a valid parent ID, update it
            if ($parentCategoryId) {
                $category->setData('parent_id', $parentCategoryId);
            }

            $this->categoryResource->save($category);

            // Fix path if we did change the parent
            if ($parentCategoryId && $parentCategoryPath) {
                $category->setPath($parentCategoryPath . '/' . $category->getId());
                $this->categoryResource->save($category);
            }

            // Store the final ID in data
            $data['Category ID'] = $category->getId();

            $this->logger->info("...Successfully updated category with External Id: {$data['MadHat External Cat Id']} (ID: {$category->getId()})");
        } catch (\Exception $e) {
            $this->logger->error("...Failed to update category attributes: {$e->getMessage()}");
        }
    }

    /**
     * Perform the menu instruction (Add, Delete, or Replace) on the Ninja Menus JSON for "top-menu".
     *
     * @param AdapterInterface $connection
     * @param string           $menuTable
     * @param string           $menuInstruction The CSV "Menu" cell, e.g. "3D-Printers & more=+>SLS Printers"
     * @param array            $data            The CSV row data, containing Title & Category ID
     * @return array|null      The updated JSON array or null if error
     */
    protected function updateMenuJson(
        AdapterInterface $connection,
        string $menuTable,
        string $menuInstruction,
        array $data
    ) {
        $this->logger->info("UpdateCategoryV2: Updating menu JSON for menu instruction: {$menuInstruction}");

        // Fetch existing JSON
        $jsonString = $connection->fetchOne(
            $connection->select()
                ->from($menuTable, ['profile'])
                ->where('identifier = ?', 'top-menu')
        );

        if (!$jsonString) {
            $this->logger->warning("UpdateCategoryV2: No existing menu JSON found for 'top-menu'.");
            return null;
        }

        $menuJson = json_decode($jsonString, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->logger->error("UpdateCategoryV2: JSON decoding error: " . json_last_error_msg());
            return null;
        }

        if (empty($menuJson) || !isset($menuJson['elements']) || !is_array($menuJson['elements'])) {
            $this->logger->error("UpdateCategoryV2: Malformed menu JSON. Missing 'elements' key.");
            return null;
        }

        // Parse the CSV "Menu" instruction
        $parsed = $this->parseMenuInstruction($menuInstruction);
        if (!$parsed) {
            $this->logger->warning("UpdateCategoryV2: Unrecognized menu instruction format: {$menuInstruction}");
            return null;
        }
        $parentTitle = $parsed['parent'];
        $operation   = $parsed['op'];      // '+', '-', or '~'
        $targetTitle = $parsed['target'];  // e.g. "Resin Printers"

        // Dispatch the operation
        switch ($operation) {
            case '+':
                $this->insertMenuItem($menuJson['elements'], $parentTitle, $targetTitle, $data);
                break;
            case '-':
                $this->deleteMenuItem($menuJson['elements'], $parentTitle, $targetTitle);
                break;
            case '~':
                $this->replaceMenuItem($menuJson['elements'], $parentTitle, $targetTitle, $data);
                break;
            default:
                $this->logger->warning("UpdateCategoryV2: Unknown operator '{$operation}' in '{$menuInstruction}'");
                return null;
        }

        return $menuJson;
    }

    /**
     * Insert a new menu item after $afterTitle within the subtree of $parentTitle.
     *
     * @param array  $elements    The full "elements" array from the top-level menu JSON
     * @param string $parentTitle The menu item's parent title (e.g. "3D-Printers & more")
     * @param string $afterTitle  The sibling title after which we insert our new item
     * @param array  $data        The CSV row data, containing Title & Category ID
     * @return void
     */
    protected function insertMenuItem(array &$elements, $parentTitle, $afterTitle, array $data)
    {
        // 1) Find the actual "parent" item by $parentTitle
        $parentItem = &$this->findElementByTitle($elements, $parentTitle);
        if ($parentItem === null) {
            $this->logger->warning("insertMenuItem() - parent '$parentTitle' not found anywhere. Skipping.");
            return;
        }

        // 2) Ensure the parent has an 'elements' array
        if (!isset($parentItem['elements']) || !is_array($parentItem['elements'])) {
            $parentItem['elements'] = [];
        }

        // 3) Find the array that contains $afterTitle in the parent's subtree
        $foundIndex = null;
        $foundArray = &$this->findArrayAndIndexByTitle($parentItem['elements'], $afterTitle, $foundIndex);
        if ($foundArray === null) {
            $this->logger->warning(
                "insertMenuItem() - target '$afterTitle' not found under '$parentTitle'. Appending at the end."
            );
            $foundArray = &$parentItem['elements'];
            $foundIndex = count($foundArray) - 1;
        }

        // 4) Define a template item (mirroring typical category item structure)
        $templateItem = [
            "title"                  => "",
            "item_type"              => "category",
            "label_position"         => "top_right",
            "caret"                  => "fas mgz-fa-angle-down",
            "caret_hover"            => "fas mgz-fa-angle-up",
            "item_align"             => "left",
            "device_type"            => "all",
            "background_type"        => "image",
            "background_style"       => "auto",
            "background_position"    => "center-top",
            "parallax_speed"         => 0.5,
            "mouse_parallax_size"    => 30,
            "mouse_parallax_speed"   => 10000,
            "lg_background_type"     => "image",
            "lg_background_style"    => "auto",
            "lg_background_position" => "center-top",
            "md_background_type"     => "image",
            "md_background_style"    => "auto",
            "md_background_position" => "center-top",
            "sm_background_type"     => "image",
            "sm_background_style"    => "auto",
            "sm_background_position" => "center-top",
            "xs_background_type"     => "image",
            "xs_background_style"    => "auto",
            "xs_background_position" => "center-top",
            "icon_position"          => "left",
            "icon"                   => "",
            "icon_hover"             => "",
            "submenu_type"           => "mega",
            "submenu_position"       => "left_edge_parent_item",
            "subcategories_col"      => 3,
            "submenu_fullwidth"      => 0,
            "type"                   => "menu_item",
            "elements"               => [],
            "category_id"            => "",
            "cat_name"               => true
        ];

        // 5) Override with CSV data
        $templateItem['title']       = $data['Title'];
        $templateItem['category_id'] = $data['Category ID'] ?: '';
        // We add a random ID if needed
        $templateItem['id']          = $this->generateRandomId();

        // 6) Insert right after $foundIndex
        if ($foundIndex !== null && $foundIndex >= 0) {
            array_splice($foundArray, $foundIndex + 1, 0, [$templateItem]);
            $this->logger->info(
                "Inserted new menu item '{$templateItem['title']}' right after '$afterTitle' under '$parentTitle'."
            );
        } else {
            // If target wasn't found, we append at the end
            $foundArray[] = $templateItem;
            $this->logger->info(
                "Inserted new menu item '{$templateItem['title']}' at end of '$parentTitle' since '$afterTitle' not found."
            );
        }
    }

    /**
     * (Optional) Generate a random 'id' for the new menu item, if your menu system needs unique IDs.
     *
     * @return string
     */
    protected function generateRandomId()
    {
        return substr(md5(uniqid('', true)), 0, 7);
    }

    /**
     * Delete a menu item titled $targetTitle under $parentTitle in the Ninja Menus JSON.
     * This will recursively search and delete all items with matching title under the parent.
     *
     * @param array  $elements
     * @param string $parentTitle
     * @param string $targetTitle
     * @return void
     */
    protected function deleteMenuItem(array &$elements, $parentTitle, $targetTitle)
    {
        // First find the parent item by title
        $parentItem = &$this->findElementByTitle($elements, $parentTitle);
        if ($parentItem === null) {
            $this->logger->warning(
                "UpdateCategoryV2: deleteMenuItem() - parent title '{$parentTitle}' not found."
            );
            return;
        }

        // Make sure 'elements' is defined
        if (!isset($parentItem['elements']) || !is_array($parentItem['elements'])) {
            $parentItem['elements'] = [];
            return;
        }

        // Now recursively delete all items with matching title in the parent's subtree
        $itemsRemoved = $this->recursiveDeleteByTitle($parentItem['elements'], $targetTitle);

        if ($itemsRemoved > 0) {
            $this->logger->info(
                "UpdateCategoryV2: Successfully deleted {$itemsRemoved} menu item(s) '{$targetTitle}' from parent '{$parentTitle}'."
            );
        } else {
            $this->logger->warning(
                "UpdateCategoryV2: deleteMenuItem() - child title '{$targetTitle}' not found under '{$parentTitle}'."
            );
        }
    }

    /**
     * Recursively delete all menu items with the specified title from the elements array.
     *
     * @param array  $elements
     * @param string $titleToDelete
     * @return int Number of items deleted
     */
    protected function recursiveDeleteByTitle(array &$elements, $titleToDelete)
    {
        $itemsRemoved = 0;

        // Loop in reverse order to safely remove items while iterating
        for ($i = count($elements) - 1; $i >= 0; $i--) {
            // Check if current item has the title we want to delete
            if (isset($elements[$i]['title']) && $elements[$i]['title'] === $titleToDelete) {
                array_splice($elements, $i, 1);
                $itemsRemoved++;
                continue;
            }

            // Recursively check children elements
            if (!empty($elements[$i]['elements']) && is_array($elements[$i]['elements'])) {
                $itemsRemoved += $this->recursiveDeleteByTitle($elements[$i]['elements'], $titleToDelete);
            }
        }

        return $itemsRemoved;
    }

    /**
     * Replace a menu item titled $targetTitle with the CSV "Title" & "Category ID"
     * **anywhere** under $parentTitle (recursively).
     *
     * @param array  $elements
     * @param string $parentTitle
     * @param string $targetTitle
     * @param array  $data
     * @return void
     */
    protected function replaceMenuItem(array &$elements, $parentTitle, $targetTitle, $data)
    {
        // First find the parent item by $parentTitle
        $parentItem = &$this->findElementByTitle($elements, $parentTitle);
        if ($parentItem === null) {
            $this->logger->warning(
                "UpdateCategoryV2: replaceMenuItem() - parent title '{$parentTitle}' not found."
            );
            return;
        }

        // Make sure 'elements' is defined
        if (!isset($parentItem['elements']) || !is_array($parentItem['elements'])) {
            $parentItem['elements'] = [];
        }

        // Now that we have a reference to the parent's element data,
        // let's do a recursive search for the child with title=$targetTitle
        $targetItem = &$this->findElementByTitle($parentItem['elements'], $targetTitle);
        if ($targetItem === null) {
            // Could not find the child anywhere under the parent
            $this->logger->warning(
                "UpdateCategoryV2: replaceMenuItem() - child title '{$targetTitle}' not found under '{$parentTitle}'."
            );
            return;
        }

        // We found the target item => replace its title & category_id
        $oldTitle = $targetItem['title'];
        $targetItem['title']       = $data['Title'];
        $targetItem['category_id'] = $data['Category ID'] ?: '';
        $targetItem['item_type']   = 'category';

        $this->logger->info(
            "UpdateCategoryV2: Replaced menu item '{$oldTitle}' with '{$targetItem['title']}' (ID: {$targetItem['category_id']})."
        );
    }

    /**
     * Recursively search the entire "elements" tree for an element whose "title" = $titleToFind.
     * Return a reference to that element if found, otherwise null.
     *
     * @param array  $elements
     * @param string $titleToFind
     * @return array|null
     */
    protected function &findElementByTitle(array &$elements, string $titleToFind)
    {
        foreach ($elements as $index => &$element) {
            if (isset($element['title']) && $element['title'] === $titleToFind) {
                return $element; // Return reference to the found element
            }
            if (!empty($element['elements']) && is_array($element['elements'])) {
                $found = &$this->findElementByTitle($element['elements'], $titleToFind);
                if ($found !== null) {
                    return $found;
                }
            }
        }
        // Return null by reference
        $null = null;
        return $null;
    }

    /**
     * Recursively find the parent's 'elements' array by parent title.
     *
     * E.g., find the item whose title = $parentTitle, then return a reference to its 'elements' subarray.
     *
     * @param array  $elements
     * @param string $parentTitle
     * @return array|null
     */
    protected function &findParentChildrenArray(array &$elements, string $parentTitle)
    {
        foreach ($elements as &$element) {
            if (isset($element['title']) && $element['title'] === $parentTitle) {
                if (!isset($element['elements']) || !is_array($element['elements'])) {
                    $element['elements'] = [];
                }
                return $element['elements']; // Return reference
            }
            if (!empty($element['elements']) && is_array($element['elements'])) {
                $found = &$this->findParentChildrenArray($element['elements'], $parentTitle);
                if ($found !== null) {
                    return $found;
                }
            }
        }
        $null = null;
        return $null;
    }

    /**
     * Parse a "Menu" instruction string of the form: "Parent=+>Target"
     *
     * Where:
     *   =+> means insert
     *   =-> means delete
     *   =~> means replace
     *
     * @param string $menuInstruction E.g. "3D-Printers & more=+>SLS Printers"
     * @return array|null  [ 'parent' => string, 'op' => '+|-|~', 'target' => string ]
     */
    protected function parseMenuInstruction($menuInstruction)
    {
        $pattern = '/^(.*?)=(\+|\-|~)>(.*)$/';
        if (preg_match($pattern, $menuInstruction, $matches)) {
            return [
                'parent' => trim($matches[1]),
                'op'     => $matches[2],
                'target' => trim($matches[3]),
            ];
        }
        return null; // Unrecognized format
    }

    /**
     * Recursively find the array + index containing an element with title = $titleToFind.
     * Return a reference to that array, and set $foundIndex to the position of the element in that array.
     *
     * @param array    $elements
     * @param string   $titleToFind
     * @param int|null $foundIndex
     * @return array|null
     */
    protected function &findArrayAndIndexByTitle(array &$elements, string $titleToFind, ?int &$foundIndex)
    {
        foreach ($elements as $i => &$el) {
            if (isset($el['title']) && $el['title'] === $titleToFind) {
                $foundIndex = $i;
                return $elements; // Return the array that holds $el
            }
            if (!empty($el['elements']) && is_array($el['elements'])) {
                $childFound = &$this->findArrayAndIndexByTitle($el['elements'], $titleToFind, $foundIndex);
                if ($childFound !== null) {
                    return $childFound;
                }
            }
        }
        $null = null;
        return $null;
    }

    /**
     * Return array of dependencies for this Data Patch (none).
     *
     * @return array
     */
    public static function getDependencies()
    {
        return [];
    }

    /**
     * Return array of aliases for this Data Patch (none).
     *
     * @return array
     */
    public function getAliases()
    {
        return [];
    }
}
