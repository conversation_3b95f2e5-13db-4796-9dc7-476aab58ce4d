<?php

/** @var $block \MadHat\DbLogger\Block\Adminhtml\DbLoggerDetails */
/** @var \Magento\Framework\Escaper $escaper */

$dbLoggerDetails = $block->getDbLoggerDetails();
?>

<div class="admin__page-section-item">
    <div class="admin__page-section-item-content">
        <table class="admin__table-secondary">
            <tr>
                <th><?= $block->escapeHtml(__('identifier')) ?></th>
                <td><?= $dbLoggerDetails->getIdentifier() ?></td>
            </tr>
            <tr>
                <th><?= $block->escapeHtml(__('Type')) ?></th>
                <td><?= $dbLoggerDetails->getType() ?></td>
            </tr>
            <tr>
                <th><?= $block->escapeHtml(__('Title')) ?></th>
                <td><?= $dbLoggerDetails->getTitle() ?></td>
            </tr>
            <tr>
                <th><?= $block->escapeHtml(__('Created')) ?></th>
                <td><?= $dbLoggerDetails->getCreatedAt() ?></td>
            </tr>
            <tr>
                <th><?= $block->escapeHtml(__('Message')) ?></th>
                <td><?= $dbLoggerDetails->getMessage() ?></td>
            </tr>
        </table>
    </div>
</div>
