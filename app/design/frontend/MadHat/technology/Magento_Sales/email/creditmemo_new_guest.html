<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<!--@subject {{trans "Credit memo for your %store_name order" store_name=$store.frontend_name}} @-->
<!--@vars {
"var formattedBillingAddress|raw":"Billing Address",
"var comment|escape|nl2br":"Credit Memo Comment",
"var creditmemo.increment_id":"Credit Memo Id",
"layout handle=\"sales_email_order_creditmemo_items\" creditmemo=$creditmemo order=$order":"Credit Memo Items Grid",
"var order_data.customer_name":"Guest Customer Name (Billing)",
"var order.increment_id":"Order Id",
"var payment_html|raw":"Payment Details",
"var formattedShippingAddress|raw":"Shipping Address",
"var order.shipping_description":"Shipping Description",
"var store.frontend_name":"Store Frontend Name",
"var store_phone":"Store Phone",
"var store_email":"Store Email",
"var store_hours":"Store Hours",
"var creditmemo":"Credit Memo",
"var order":"Order",
"var order_data.is_not_virtual":"Order Type",
"var order_id": "Order DB Id",
"var creditmemo_id": "Credit Memo DB Id"
} @-->
{{template config_path="design/email/header_template"}}

<table>
    <tr class="email-intro">
        <td>
            <h1 class="page-title more-padding">{{trans "Your order has been %order_status" order_status=$order_data.frontend_status_label}}</h1>
            <p class="text-center mb-20">
                {{trans
                    "%name, Your order <strong>#%increment_id</strong> has been updated with a status of <strong>%order_status</strong>. You can check the status of your order by logging into <a href="%account_url">your account</a>."

                    name=$order_data.customer_name
                    increment_id=$order.increment_id
                    order_status=$order_data.frontend_status_label
                    account_url=$this.getUrl($store,'customer/account/',[_nosid:1])
                |raw}}
            </p>
            <p class="text-center">
                {{trans
                    'If you have questions about your order, you can email us at <a href="mailto:%store_email">%store_email</a>.' 
                    store_email=$store_email
                |raw}}
            </p>
            
        </td>
    </tr>
    <tr class="email-summary">
        <td>
            <h1 class="page-title">{{trans 'Your Credit Memo <span class="blue-color">#%creditmemo_id</span> for Order <span class="blue-color">#%order_id</span>' creditmemo_id=$creditmemo.increment_id order_id=$order.increment_id |raw}}</h1>
        </td>
    </tr>
    <tr class="email-information">
        <td>
            {{depend comment}}
            <table class="message-info">
                <tr>
                    <td>
                        {{var comment|escape|nl2br}}
                    </td>
                </tr>
            </table>
            {{/depend}}
            <table class="order-details">
                <tr>
                    <td class="address-details">
                        <h3>{{trans "Billing Info"}}</h3>
                        <p>{{var formattedBillingAddress|raw}}</p>
                    </td>
                    {{depend order_data.is_not_virtual}}
                    <td class="address-details">
                        <h3>{{trans "Shipping Info"}}</h3>
                        <p>{{var formattedShippingAddress|raw}}</p>
                    </td>
                    {{/depend}}
                </tr>
                <tr>
                    <td class="method-info">
                        <h3>{{trans "Payment Method"}}</h3>
                        {{var payment_html|raw}}
                    </td>
                    {{depend order_data.is_not_virtual}}
                    <td class="method-info">
                        <h3>{{trans "Shipping Method"}}</h3>
                        <p>{{var order.shipping_description}}</p>
                    </td>
                    {{/depend}}
                </tr>
            </table>
            {{layout handle="sales_email_order_creditmemo_items" creditmemo_id=$creditmemo_id order_id=$order_id}}
        </td>
    </tr>
</table>

{{template config_path="design/email/footer_template"}}
