<?php
/**
 * Copyright © Magefan (<EMAIL>). All rights reserved.
 * Please visit Magefan.com for license details (https://magefan.com/end-user-license-agreement).
 */
?>
<?php
/**
 * Blog sidebar archive template
 *
 * @var $block \Magefan\Blog\Block\Sidebar\Archive
 */
?>

<?php
    $_months = $block->getMonths();
?>
<?php if (count($_months)) { ?>
<div class="block-archive mb-10">
   <div class="title text-xl font-semibold mb-3">
        <?= $escaper->escapeHtml(__('Archive')) ?>
    </div>
    <div class="block-content flex flex-col gap-2">
        <?php foreach ($_months as $time) { ?>
            <?php
                $title = $block->getTranslatedDate($time);
            ?>
            <div class="item">
                <a title="<?= $escaper->escapeHtml(__('Archive %1', $title)) ?>"
                   class="archive-item-link hover:underline"
                   href="<?= $escaper->escapeUrl($block->getTimeUrl($time)) ?>">
                    <?= $escaper->escapeHtml($title); ?>
                </a>
            </div>
        <?php } ?>
    </div>
</div>
<?php } ?>


