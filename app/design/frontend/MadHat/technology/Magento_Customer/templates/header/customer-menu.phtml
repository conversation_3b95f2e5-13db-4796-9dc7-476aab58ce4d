<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Customer\Block\Account\Customer;
use Magento\Framework\Escaper;

/** @var Customer $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);
?>

<div
    class="relative inline-block"
    x-data="initCustomerMenu()"
    @private-content-loaded.window="getData($event.detail.data)"
    x-bind="customerMenuListeners"
>
    <button
        type="button"
        id="customer-menu"
        class="block rounded p-1 outline-offset-2"
        @click="open = !open"
        :aria-expanded="open ? 'true' : 'false'"
        aria-label="<?= $escaper->escapeHtmlAttr(__('My Account')) ?>"
        aria-haspopup="true"
    >
        <?= $heroicons->userCircleHtml('', 24, 24, ['aria-hidden' => 'true']); ?>
    </button>
    <nav
        class="absolute right-0 z-20 w-40 text-sm font-semibold py-2 mt-2 -mr-4 px-1 overflow-auto origin-top-right rounded-sm
            shadow-lg sm:w-48 lg:mt-3 bg-container-lighter z-50"
        x-cloak
        x-show="open"
        x-transition
        aria-labelledby="customer-menu"
        id="navCustomerMenu"
    >

        <div id="logged-in-menu">
            <?= $block->getChildHtml('header.customer.logged.in.links') ?>
        </div>
        <div id="logged-out-menu">
            <?= $block->getChildHtml('header.customer.logged.out.links') ?>
        </div>
    </nav>
</div>
<script>
    function initCustomerMenu() {
        return {
            open: false,
            getData(data) {
                if (data.customer.email) {
                    document.getElementById('logged-out-menu').classList.add('hidden');
                    document.getElementById('logged-in-menu').classList.remove('hidden');
                } else {
                    document.getElementById('logged-in-menu').classList.add('hidden');
                    document.getElementById('logged-out-menu').classList.remove('hidden');
                }
            },
            customerMenuListeners: {
                ['@private-content-loaded.window'](event) {
                    this.getData(event.detail.data);
                }
            }
        }
    }
</script>
